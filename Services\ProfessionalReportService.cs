using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Windows;
using MigraDoc.DocumentObjectModel;
using MigraDoc.DocumentObjectModel.Tables;
using MigraDoc.Rendering;
using PdfSharp.Pdf;
using PdfSharp.Fonts;
using DriverManagementSystem.ViewModels;
using DriverManagementSystem.Models;

namespace DriverManagementSystem.Services
{
    /// <summary>
    /// FontResolver مخصص لحل مشكلة الخطوط
    /// </summary>
    public class SafeFontResolver : IFontResolver
    {
        public string DefaultFontName => "Times New Roman";

        public FontResolverInfo ResolveTypeface(string familyName, bool isBold, bool isItalic)
        {
            // استخدام Times New Roman لجميع الخطوط
            return new FontResolverInfo("TimesNewRoman");
        }

        public byte[] GetFont(string faceName)
        {
            try
            {
                // محاولة تحميل Times New Roman
                var fontPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Windows), "Fonts", "times.ttf");
                if (File.Exists(fontPath))
                {
                    return File.ReadAllBytes(fontPath);
                }

                // البديل: Arial
                fontPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Windows), "Fonts", "arial.ttf");
                if (File.Exists(fontPath))
                {
                    return File.ReadAllBytes(fontPath);
                }
            }
            catch
            {
                // في حالة الفشل، إرجاع null
            }

            return null;
        }
    }

    /// <summary>
    /// خدمة طباعة احترافية باستخدام MigraDoc/PdfSharp
    /// </summary>
    public static class ProfessionalReportService
    {
        /// <summary>
        /// طباعة تقرير الزيارة الميدانية
        /// </summary>
        public static void PrintFieldVisitReport(ReportViewModel viewModel)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🚀 بدء إنشاء التقرير الاحترافي...");

                // تعيين FontResolver الآمن لحل مشكلة الخطوط
                GlobalFontSettings.FontResolver = new SafeFontResolver();

                // إنشاء مستند بسيط
                var document = CreateSimpleDocument(viewModel);

                // حفظ كـ PDF مباشرة
                SaveAsPdf(document);

                System.Diagnostics.Debug.WriteLine("✅ تم إكمال التقرير بنجاح");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إنشاء التقرير: {ex.Message}");
                MessageBox.Show($"حدث خطأ في إنشاء التقرير:\n{ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// إنشاء مستند بسيط
        /// </summary>
        private static Document CreateSimpleDocument(ReportViewModel viewModel)
        {
            var document = new Document();

            // تعيين خط Times New Roman لجميع الأنماط بشكل صريح
            document.Styles["Normal"].Font.Name = "Times New Roman";
            document.Styles["Normal"].Font.Size = 12;

            // تعيين الخط للأنماط الأساسية بشكل صريح
            if (document.Styles["Heading1"] != null)
                document.Styles["Heading1"].Font.Name = "Times New Roman";
            if (document.Styles["Heading2"] != null)
                document.Styles["Heading2"].Font.Name = "Times New Roman";
            if (document.Styles["Title"] != null)
                document.Styles["Title"].Font.Name = "Times New Roman";

            // تطبيق نفس الخط على جميع الأنماط لضمان عدم استخدام أي خط آخر
            foreach (var styleObj in document.Styles)
            {
                if (styleObj is MigraDoc.DocumentObjectModel.Style style)
                {
                    style.Font.Name = "Times New Roman";
                }
            }

            // إضافة قسم
            var section = document.AddSection();
            section.PageSetup.PageFormat = PageFormat.A4;
            section.PageSetup.TopMargin = "2cm";
            section.PageSetup.BottomMargin = "2cm";
            section.PageSetup.LeftMargin = "2cm";
            section.PageSetup.RightMargin = "2cm";

            // عنوان التقرير
            var title = section.AddParagraph("تقرير الزيارة الميدانية");
            title.Format.Font.Name = "Times New Roman";
            title.Format.Font.Size = 18;
            title.Format.Font.Bold = true;
            title.Format.Alignment = ParagraphAlignment.Center;
            title.Format.SpaceAfter = 20;

            // معلومات الزيارة
            var info = section.AddParagraph();
            info.Format.Font.Name = "Times New Roman";
            info.Format.Font.Size = 12;
            info.AddText($"رقم الزيارة: {viewModel.ReportData.VisitNumber}\n");
            info.AddText($"التاريخ: {DateTime.Now:yyyy/MM/dd}\n");
            info.AddText($"الوقت: {DateTime.Now:HH:mm}\n");
            info.Format.SpaceAfter = 20;

            // جدول المشاريع
            if (viewModel.ReportData.Projects?.Count > 0)
            {
                var projectsTitle = section.AddParagraph("المشاريع");
                projectsTitle.Format.Font.Name = "Times New Roman";
                projectsTitle.Format.Font.Size = 14;
                projectsTitle.Format.Font.Bold = true;
                projectsTitle.Format.SpaceAfter = 10;

                var projectsTable = section.AddTable();
                projectsTable.Borders.Width = 0.75;
                projectsTable.Borders.Color = Colors.Black;

                // أعمدة الجدول
                projectsTable.AddColumn("2cm");
                projectsTable.AddColumn("6cm");
                projectsTable.AddColumn("8cm");

                // رأس الجدول
                var headerRow = projectsTable.AddRow();
                headerRow.Format.Font.Name = "Times New Roman";
                headerRow.Format.Font.Bold = true;
                headerRow.Shading.Color = Colors.LightGray;

                var cell1 = headerRow.Cells[0].AddParagraph("رقم");
                cell1.Format.Alignment = ParagraphAlignment.Center;

                var cell2 = headerRow.Cells[1].AddParagraph("رقم المشروع");
                cell2.Format.Alignment = ParagraphAlignment.Center;

                var cell3 = headerRow.Cells[2].AddParagraph("اسم المشروع");
                cell3.Format.Alignment = ParagraphAlignment.Center;

                // بيانات المشاريع
                int index = 1;
                foreach (var project in viewModel.ReportData.Projects)
                {
                    var row = projectsTable.AddRow();
                    row.Format.Font.Name = "Times New Roman";

                    var dataCell1 = row.Cells[0].AddParagraph(index.ToString());
                    dataCell1.Format.Alignment = ParagraphAlignment.Center;

                    var dataCell2 = row.Cells[1].AddParagraph(project.ProjectNumber ?? "");
                    dataCell2.Format.Alignment = ParagraphAlignment.Center;

                    var dataCell3 = row.Cells[2].AddParagraph(project.ProjectName ?? "");
                    dataCell3.Format.Alignment = ParagraphAlignment.Right;

                    index++;
                }
            }

            return document;
        }

        /// <summary>
        /// إنشاء مستند التقرير
        /// </summary>
        private static Document CreateReportDocument(ReportViewModel viewModel)
        {
            // إنشاء المستند
            var document = new Document();
            document.Info.Title = "تقرير الزيارة الميدانية";
            document.Info.Author = "صندوق التنمية الاجتماعية";
            document.Info.Subject = $"زيارة رقم {viewModel.ReportData.VisitNumber}";

            // تعريف الأنماط
            DefineStyles(document);

            // إضافة القسم الرئيسي
            var section = document.AddSection();
            SetupPageLayout(section);

            // إضافة محتوى التقرير
            AddReportHeader(section);
            AddReportTitle(section, viewModel);
            AddVisitInformation(section, viewModel);
            AddProjectsTable(section, viewModel);
            AddOffersTable(section, viewModel);
            AddReportFooter(section);

            return document;
        }

        /// <summary>
        /// تعريف أنماط المستند
        /// </summary>
        private static void DefineStyles(Document document)
        {
            // النمط العادي
            var style = document.Styles["Normal"];
            style.Font.Name = "Times New Roman"; // خط آمن ومدعوم
            style.Font.Size = 12;

            // نمط العنوان الرئيسي
            style = document.Styles.AddStyle("Title", "Normal");
            style.Font.Size = 18;
            style.Font.Bold = true;
            style.ParagraphFormat.Alignment = ParagraphAlignment.Center;
            style.ParagraphFormat.SpaceAfter = 20;

            // نمط العنوان الفرعي
            style = document.Styles.AddStyle("Subtitle", "Normal");
            style.Font.Size = 14;
            style.Font.Bold = true;
            style.ParagraphFormat.SpaceAfter = 10;
            style.ParagraphFormat.SpaceBefore = 15;

            // نمط رأس الجدول
            style = document.Styles.AddStyle("TableHeader", "Normal");
            style.Font.Bold = true;
            style.ParagraphFormat.Alignment = ParagraphAlignment.Center;

            // نمط خلية الجدول
            style = document.Styles.AddStyle("TableCell", "Normal");
            style.ParagraphFormat.Alignment = ParagraphAlignment.Center;
        }

        /// <summary>
        /// إعداد تخطيط الصفحة
        /// </summary>
        private static void SetupPageLayout(Section section)
        {
            // إعداد الصفحة
            section.PageSetup.PageFormat = PageFormat.A4;
            section.PageSetup.Orientation = Orientation.Portrait;
            section.PageSetup.TopMargin = "2cm";
            section.PageSetup.BottomMargin = "2cm";
            section.PageSetup.LeftMargin = "2cm";
            section.PageSetup.RightMargin = "2cm";

            // ترقيم الصفحات
            var paragraph = section.Footers.Primary.AddParagraph();
            paragraph.AddText("صفحة ");
            paragraph.AddPageField();
            paragraph.AddText(" من ");
            paragraph.AddNumPagesField();
            paragraph.Format.Alignment = ParagraphAlignment.Center;
        }

        /// <summary>
        /// إضافة رأس التقرير
        /// </summary>
        private static void AddReportHeader(Section section)
        {
            var table = section.AddTable();
            table.Style = "Table";
            table.Borders.Width = 0;
            table.Rows.LeftIndent = 0;

            // تعريف الأعمدة
            var column = table.AddColumn("5cm");
            column.Format.Alignment = ParagraphAlignment.Left;
            column = table.AddColumn("8cm");
            column.Format.Alignment = ParagraphAlignment.Center;
            column = table.AddColumn("5cm");
            column.Format.Alignment = ParagraphAlignment.Right;

            // إضافة الصف
            var row = table.AddRow();
            row.Cells[0].AddParagraph("🏢 SFD");
            row.Cells[1].AddParagraph("صندوق التنمية الاجتماعية").Style = "TableHeader";
            row.Cells[2].AddParagraph($"التاريخ: {DateTime.Now:yyyy/MM/dd}");

            // خط فاصل
            var separator = section.AddParagraph();
            separator.Format.Borders.Bottom.Width = 1;
            separator.Format.SpaceAfter = 15;
        }

        /// <summary>
        /// إضافة عنوان التقرير
        /// </summary>
        private static void AddReportTitle(Section section, ReportViewModel viewModel)
        {
            var title = section.AddParagraph("تقرير الزيارة الميدانية");
            title.Style = "Title";
        }

        /// <summary>
        /// إضافة معلومات الزيارة
        /// </summary>
        private static void AddVisitInformation(Section section, ReportViewModel viewModel)
        {
            var subtitle = section.AddParagraph("معلومات الزيارة");
            subtitle.Style = "Subtitle";

            var table = section.AddTable();
            table.Style = "Table";
            table.Borders.Width = 0.75;
            table.Rows.LeftIndent = 0;

            // تعريف الأعمدة
            var column = table.AddColumn("4cm");
            column.Format.Alignment = ParagraphAlignment.Right;
            column = table.AddColumn("10cm");
            column.Format.Alignment = ParagraphAlignment.Right;

            // إضافة البيانات
            AddInfoRow(table, "رقم الزيارة:", viewModel.ReportData.VisitNumber?.ToString() ?? "غير محدد");
            AddInfoRow(table, "التاريخ:", DateTime.Now.ToString("yyyy/MM/dd"));
            AddInfoRow(table, "الوقت:", DateTime.Now.ToString("HH:mm"));
            AddInfoRow(table, "القطاع:", "قطاع التنمية");
            AddInfoRow(table, "مسؤول الزيارة:", "مسؤول الزيارة الميدانية");

            table.SetEdge(0, 0, 2, table.Rows.Count, Edge.Box, BorderStyle.Single, 0.75, Colors.Black);
        }

        /// <summary>
        /// إضافة صف معلومات
        /// </summary>
        private static void AddInfoRow(Table table, string label, string value)
        {
            var row = table.AddRow();
            row.Cells[0].AddParagraph(label).Style = "TableHeader";
            row.Cells[0].Shading.Color = Colors.LightGray;
            row.Cells[1].AddParagraph(value).Style = "TableCell";
        }

        /// <summary>
        /// إضافة جدول المشاريع
        /// </summary>
        private static void AddProjectsTable(Section section, ReportViewModel viewModel)
        {
            if (viewModel.ReportData.Projects?.Count == 0)
                return;

            var subtitle = section.AddParagraph("المشاريع");
            subtitle.Style = "Subtitle";

            var table = section.AddTable();
            table.Style = "Table";
            table.Borders.Width = 0.75;
            table.Rows.LeftIndent = 0;
            table.KeepTogether = true; // منع تقسيم الجدول

            // تعريف الأعمدة
            table.AddColumn("2cm").Format.Alignment = ParagraphAlignment.Center;
            table.AddColumn("4cm").Format.Alignment = ParagraphAlignment.Center;
            table.AddColumn("8cm").Format.Alignment = ParagraphAlignment.Center;

            // رأس الجدول
            var headerRow = table.AddRow();
            headerRow.HeadingFormat = true;
            headerRow.Format.Font.Bold = true;
            headerRow.Shading.Color = Colors.LightGray;
            headerRow.Cells[0].AddParagraph("رقم").Style = "TableHeader";
            headerRow.Cells[1].AddParagraph("رقم المشروع").Style = "TableHeader";
            headerRow.Cells[2].AddParagraph("اسم المشروع").Style = "TableHeader";

            // بيانات الجدول
            int projectIndex = 1;
            foreach (var project in viewModel.ReportData.Projects)
            {
                var row = table.AddRow();
                row.KeepWith = 1; // منع تقسيم الصف
                row.Cells[0].AddParagraph(projectIndex.ToString()).Style = "TableCell";
                row.Cells[1].AddParagraph(project.ProjectNumber ?? "").Style = "TableCell";
                row.Cells[2].AddParagraph(project.ProjectName ?? "").Style = "TableCell";
                projectIndex++;
            }

            table.SetEdge(0, 0, 3, table.Rows.Count, Edge.Box, BorderStyle.Single, 0.75, Colors.Black);
        }

        /// <summary>
        /// إضافة جدول عروض الأسعار
        /// </summary>
        private static void AddOffersTable(Section section, ReportViewModel viewModel)
        {
            if (viewModel.ReportData.PriceOffers?.Count == 0)
                return;

            var subtitle = section.AddParagraph("عروض الأسعار");
            subtitle.Style = "Subtitle";

            var table = section.AddTable();
            table.Style = "Table";
            table.Borders.Width = 0.75;
            table.Rows.LeftIndent = 0;
            table.KeepTogether = true;

            // تعريف الأعمدة
            table.AddColumn("2cm").Format.Alignment = ParagraphAlignment.Center;
            table.AddColumn("4cm").Format.Alignment = ParagraphAlignment.Center;
            table.AddColumn("4cm").Format.Alignment = ParagraphAlignment.Center;
            table.AddColumn("4cm").Format.Alignment = ParagraphAlignment.Center;

            // رأس الجدول
            var headerRow = table.AddRow();
            headerRow.HeadingFormat = true;
            headerRow.Format.Font.Bold = true;
            headerRow.Shading.Color = Colors.LightGray;
            headerRow.Cells[0].AddParagraph("رقم").Style = "TableHeader";
            headerRow.Cells[1].AddParagraph("اسم السائق").Style = "TableHeader";
            headerRow.Cells[2].AddParagraph("رقم الهاتف").Style = "TableHeader";
            headerRow.Cells[3].AddParagraph("السعر").Style = "TableHeader";

            // بيانات الجدول
            int offerIndex = 1;
            foreach (var offer in viewModel.ReportData.PriceOffers)
            {
                var row = table.AddRow();
                row.KeepWith = 1;
                row.Cells[0].AddParagraph(offerIndex.ToString()).Style = "TableCell";
                row.Cells[1].AddParagraph(offer.DriverName ?? "").Style = "TableCell";
                row.Cells[2].AddParagraph(offer.PhoneNumber ?? "").Style = "TableCell";
                row.Cells[3].AddParagraph(offer.OfferedPrice.ToString("N0")).Style = "TableCell";
                offerIndex++;
            }

            table.SetEdge(0, 0, 4, table.Rows.Count, Edge.Box, BorderStyle.Single, 0.75, Colors.Black);
        }

        /// <summary>
        /// إضافة تذييل التقرير
        /// </summary>
        private static void AddReportFooter(Section section)
        {
            // مساحة فارغة
            section.AddParagraph().Format.SpaceAfter = 30;

            // جدول التوقيعات
            var table = section.AddTable();
            table.Style = "Table";
            table.Borders.Width = 0;
            table.Rows.LeftIndent = 0;

            table.AddColumn("9cm").Format.Alignment = ParagraphAlignment.Center;
            table.AddColumn("9cm").Format.Alignment = ParagraphAlignment.Center;

            var row = table.AddRow();
            row.Cells[0].AddParagraph("توقيع المسؤول\n\n_________________");
            row.Cells[1].AddParagraph("توقيع المراجع\n\n_________________");
        }

        /// <summary>
        /// حفظ كـ PDF
        /// </summary>
        private static void SaveAsPdf(Document document)
        {
            try
            {
                var saveDialog = new Microsoft.Win32.SaveFileDialog
                {
                    Filter = "PDF Files (*.pdf)|*.pdf",
                    DefaultExt = "pdf",
                    FileName = $"تقرير_زيارة_ميدانية_{DateTime.Now:yyyyMMdd_HHmm}.pdf"
                };

                if (saveDialog.ShowDialog() == true)
                {
                    var renderer = new PdfDocumentRenderer();
                    renderer.Document = document;
                    renderer.RenderDocument();
                    renderer.PdfDocument.Save(saveDialog.FileName);

                    MessageBox.Show($"تم حفظ التقرير بنجاح:\n{saveDialog.FileName}", "نجح الحفظ", MessageBoxButton.OK, MessageBoxImage.Information);

                    // فتح الملف
                    Process.Start(new ProcessStartInfo(saveDialog.FileName) { UseShellExecute = true });
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ PDF:\n{ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// طباعة مباشرة
        /// </summary>
        private static void PrintDirectly(Document document)
        {
            try
            {
                var renderer = new PdfDocumentRenderer();
                renderer.Document = document;
                renderer.RenderDocument();

                // حفظ مؤقت للطباعة
                var tempFile = Path.GetTempFileName().Replace(".tmp", ".pdf");
                renderer.PdfDocument.Save(tempFile);

                // طباعة الملف
                Process.Start(new ProcessStartInfo(tempFile) { UseShellExecute = true, Verb = "print" });

                MessageBox.Show("تم إرسال التقرير للطباعة", "طباعة", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في الطباعة:\n{ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}
