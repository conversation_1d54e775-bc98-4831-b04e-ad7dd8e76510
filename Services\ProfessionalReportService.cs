using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using Microsoft.Win32;
using MigraDoc.DocumentObjectModel;
using MigraDoc.DocumentObjectModel.Tables;
using MigraDoc.Rendering;
using PdfSharp.Pdf;
using PdfSharp.Fonts;
using DriverManagementSystem.ViewModels;
using DriverManagementSystem.Models;
using TableVerticalAlignment = MigraDoc.DocumentObjectModel.Tables.VerticalAlignment;

namespace DriverManagementSystem.Services
{
    /// <summary>
    /// FontResolver مخصص لحل مشكلة الخطوط
    /// </summary>
    public class SafeFontResolver : IFontResolver
    {
        public string DefaultFontName => "Times New Roman";

        public FontResolverInfo ResolveTypeface(string familyName, bool isBold, bool isItalic)
        {
            // استخدام Times New Roman لجميع الخطوط
            return new FontResolverInfo("TimesNewRoman");
        }

        public byte[] GetFont(string faceName)
        {
            try
            {
                // محاولة تحميل Times New Roman
                var fontPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Windows), "Fonts", "times.ttf");
                if (File.Exists(fontPath))
                {
                    return File.ReadAllBytes(fontPath);
                }

                // البديل: Arial
                fontPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Windows), "Fonts", "arial.ttf");
                if (File.Exists(fontPath))
                {
                    return File.ReadAllBytes(fontPath);
                }
            }
            catch
            {
                // في حالة الفشل، إرجاع null
            }

            return null;
        }
    }

    /// <summary>
    /// خدمة طباعة احترافية باستخدام MigraDoc/PdfSharp
    /// </summary>
    public static class ProfessionalReportService
    {
        /// <summary>
        /// طباعة تقرير الزيارة الميدانية مع معاينة
        /// </summary>
        public static void PrintFieldVisitReport(ReportViewModel viewModel)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🚀 بدء إنشاء التقرير الاحترافي...");

                // تعيين FontResolver الآمن لحل مشكلة الخطوط
                GlobalFontSettings.FontResolver = new SafeFontResolver();

                // إنشاء مستند بسيط
                var document = CreateSimpleDocument(viewModel);

                // إظهار معاينة الطباعة بدلاً من الحفظ المباشر
                ShowPrintPreview(document);

                System.Diagnostics.Debug.WriteLine("✅ تم إكمال التقرير بنجاح");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إنشاء التقرير: {ex.Message}");
                MessageBox.Show($"حدث خطأ في إنشاء التقرير:\n{ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// إظهار معاينة الطباعة
        /// </summary>
        private static void ShowPrintPreview(Document document)
        {
            try
            {
                // تحويل MigraDoc إلى PDF مؤقت
                var renderer = new PdfDocumentRenderer();
                renderer.Document = document;
                renderer.RenderDocument();

                // حفظ مؤقت
                var tempFile = Path.GetTempFileName().Replace(".tmp", ".pdf");
                renderer.PdfDocument.Save(tempFile);

                // فتح معاينة الطباعة باستخدام Windows
                var startInfo = new ProcessStartInfo
                {
                    FileName = tempFile,
                    UseShellExecute = true,
                    Verb = "printto" // هذا يفتح معاينة الطباعة
                };

                Process.Start(startInfo);

                // حذف الملف المؤقت بعد فترة
                Task.Delay(5000).ContinueWith(_ =>
                {
                    try
                    {
                        if (File.Exists(tempFile))
                            File.Delete(tempFile);
                    }
                    catch { }
                });
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في معاينة الطباعة:\n{ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// إنشاء مستند احترافي مطابق للتصميم الأصلي
        /// </summary>
        private static Document CreateSimpleDocument(ReportViewModel viewModel)
        {
            var document = new Document();

            // تعيين خط Times New Roman لجميع الأنماط بشكل صريح
            document.Styles["Normal"].Font.Name = "Times New Roman";
            document.Styles["Normal"].Font.Size = 12;

            // تطبيق نفس الخط على جميع الأنماط لضمان عدم استخدام أي خط آخر
            foreach (var styleObj in document.Styles)
            {
                if (styleObj is MigraDoc.DocumentObjectModel.Style style)
                {
                    style.Font.Name = "Times New Roman";
                }
            }

            // إضافة قسم
            var section = document.AddSection();
            section.PageSetup.PageFormat = PageFormat.A4;
            section.PageSetup.TopMargin = "1.5cm";
            section.PageSetup.BottomMargin = "1.5cm";
            section.PageSetup.LeftMargin = "1.5cm";
            section.PageSetup.RightMargin = "1.5cm";

            // إضافة الترويسة الاحترافية
            AddProfessionalHeader(section, viewModel);

            // إضافة عنوان التقرير الرئيسي
            AddMainTitle(section);

            // إضافة معلومات الزيارة
            AddVisitInfo(section, viewModel);

            // إضافة جدول المشاريع الاحترافي
            AddProfessionalProjectsTable(section, viewModel);

            return document;
        }

        /// <summary>
        /// إضافة الترويسة الاحترافية
        /// </summary>
        private static void AddProfessionalHeader(Section section, ReportViewModel viewModel)
        {
            // جدول الترويسة
            var headerTable = section.AddTable();
            headerTable.Borders.Width = 1;
            headerTable.Borders.Color = Colors.Black;

            // أعمدة الترويسة
            headerTable.AddColumn("4cm");  // التاريخ ورقم الزيارة
            headerTable.AddColumn("8cm");  // العنوان الرئيسي
            headerTable.AddColumn("4cm");  // معلومات المؤسسة

            var headerRow = headerTable.AddRow();
            headerRow.Height = "2cm";
            headerRow.VerticalAlignment = TableVerticalAlignment.Center;

            // العمود الأيسر - التاريخ ورقم الزيارة
            var leftCell = headerRow.Cells[0];
            leftCell.Shading.Color = Colors.LightBlue;
            leftCell.Format.Alignment = ParagraphAlignment.Center;
            leftCell.AddParagraph($"التاريخ: {DateTime.Now:dd/MM/yyyy}").Format.Font.Size = 10;
            leftCell.AddParagraph($"رقم الزيارة: {viewModel.ReportData.VisitNumber}").Format.Font.Size = 10;

            // العمود الأوسط - العنوان الرئيسي
            var centerCell = headerRow.Cells[1];
            centerCell.Shading.Color = Colors.LightBlue;
            centerCell.Format.Alignment = ParagraphAlignment.Center;
            var titlePara = centerCell.AddParagraph("محضر استدراج عروض أسعار");
            titlePara.Format.Font.Size = 16;
            titlePara.Format.Font.Bold = true;
            titlePara.Format.Font.Name = "Times New Roman";

            // العمود الأيمن - معلومات المؤسسة
            var rightCell = headerRow.Cells[2];
            rightCell.Shading.Color = Colors.LightBlue;
            rightCell.Format.Alignment = ParagraphAlignment.Center;
            rightCell.AddParagraph("الجمهورية اليمنية").Format.Font.Size = 10;
            rightCell.AddParagraph("الصندوق الاجتماعي للتنمية").Format.Font.Size = 10;
            rightCell.AddParagraph("فرع دمار والبيضاء").Format.Font.Size = 10;
        }

        /// <summary>
        /// إضافة العنوان الرئيسي
        /// </summary>
        private static void AddMainTitle(Section section)
        {
            section.AddParagraph().Format.SpaceAfter = 10;

            var titlePara = section.AddParagraph("المشاريع التي سيتم زيارتها");
            titlePara.Format.Font.Name = "Times New Roman";
            titlePara.Format.Font.Size = 14;
            titlePara.Format.Font.Bold = true;
            titlePara.Format.Alignment = ParagraphAlignment.Center;
            titlePara.Format.SpaceAfter = 15;
        }

        /// <summary>
        /// إضافة معلومات الزيارة
        /// </summary>
        private static void AddVisitInfo(Section section, ReportViewModel viewModel)
        {
            var infoTable = section.AddTable();
            infoTable.Borders.Width = 1;
            infoTable.Borders.Color = Colors.Black;

            // أعمدة جدول المعلومات
            infoTable.AddColumn("3cm");
            infoTable.AddColumn("13cm");

            // صف النشاط
            var activityRow = infoTable.AddRow();
            activityRow.Cells[0].Shading.Color = Colors.LightGray;
            activityRow.Cells[0].AddParagraph("النشاط:").Format.Font.Bold = true;
            activityRow.Cells[1].AddParagraph("التحقق من الدراسات الفنية والأسر");

            section.AddParagraph().Format.SpaceAfter = 15;
        }

        /// <summary>
        /// إضافة جدول المشاريع الاحترافي
        /// </summary>
        private static void AddProfessionalProjectsTable(Section section, ReportViewModel viewModel)
        {
            if (viewModel.ReportData.Projects?.Any() != true)
                return;

            // جدول المشاريع الاحترافي
            var projectsTable = section.AddTable();
            projectsTable.Borders.Width = 1;
            projectsTable.Borders.Color = Colors.Black;

            // أعمدة الجدول
            projectsTable.AddColumn("2cm");   // عدد الأيام
            projectsTable.AddColumn("8cm");   // اسم المشروع
            projectsTable.AddColumn("6cm");   // رقم المشروع

            // رأس الجدول
            var headerRow = projectsTable.AddRow();
            headerRow.Height = "1cm";
            headerRow.Shading.Color = Colors.LightGray;
            headerRow.VerticalAlignment = TableVerticalAlignment.Center;

            var daysHeader = headerRow.Cells[0].AddParagraph("عدد الأيام");
            daysHeader.Format.Font.Bold = true;
            daysHeader.Format.Font.Size = 12;
            daysHeader.Format.Alignment = ParagraphAlignment.Center;

            var nameHeader = headerRow.Cells[1].AddParagraph("اسم المشروع");
            nameHeader.Format.Font.Bold = true;
            nameHeader.Format.Font.Size = 12;
            nameHeader.Format.Alignment = ParagraphAlignment.Center;

            var numberHeader = headerRow.Cells[2].AddParagraph("رقم المشروع");
            numberHeader.Format.Font.Bold = true;
            numberHeader.Format.Font.Size = 12;
            numberHeader.Format.Alignment = ParagraphAlignment.Center;

            // صفوف البيانات
            int counter = 1;
            foreach (var project in viewModel.ReportData.Projects)
            {
                var row = projectsTable.AddRow();
                row.Height = "1.5cm";
                row.VerticalAlignment = TableVerticalAlignment.Center;

                // عدد الأيام
                var daysCell = row.Cells[0];
                var daysPara = daysCell.AddParagraph(counter.ToString());
                daysPara.Format.Alignment = ParagraphAlignment.Center;
                daysPara.Format.Font.Size = 11;

                // اسم المشروع
                var nameCell = row.Cells[1];
                var namePara = nameCell.AddParagraph(project.ProjectName ?? "");
                namePara.Format.Alignment = ParagraphAlignment.Right;
                namePara.Format.Font.Size = 11;

                // رقم المشروع
                var numberCell = row.Cells[2];
                var numberPara = numberCell.AddParagraph(project.ProjectNumber ?? "");
                numberPara.Format.Alignment = ParagraphAlignment.Center;
                numberPara.Format.Font.Size = 11;

                counter++;
            }

            section.AddParagraph().Format.SpaceAfter = 20;

            // إضافة قسم بيانات الزيارة الميدانية
            AddFieldVisitDataSection(section, viewModel);
        }

        /// <summary>
        /// إضافة قسم بيانات الزيارة الميدانية
        /// </summary>
        private static void AddFieldVisitDataSection(Section section, ReportViewModel viewModel)
        {
            // عنوان القسم
            var sectionTitle = section.AddParagraph("بيانات الزيارة الميدانية");
            sectionTitle.Format.Font.Size = 14;
            sectionTitle.Format.Font.Bold = true;
            sectionTitle.Format.Alignment = ParagraphAlignment.Center;
            sectionTitle.Format.Shading.Color = Colors.SteelBlue;
            sectionTitle.Format.Font.Color = Colors.White;
            sectionTitle.Format.SpaceAfter = 15;

            // طبيعة النشاط
            var activitySection = section.AddParagraph();
            activitySection.Format.SpaceAfter = 10;
            activitySection.AddFormattedText("طبيعة النشاط: ", TextFormat.Bold);
            activitySection.AddText("التحقق من الدراسات الفنية والأسر");

            // القائم بالزيارة
            var conductorSection = section.AddParagraph();
            conductorSection.Format.SpaceAfter = 10;
            conductorSection.AddFormattedText("القائم بالزيارة: ", TextFormat.Bold);
            conductorSection.AddText("عبدالله علي ناصر الأرحبي - غالب عبدالله علي عذرة");

            // خط السير ووصف الرسالة المرسلة للسائقين
            AddDriverMessageSection(section);

            // تواريخ الزيارة
            AddVisitDatesSection(section, viewModel);
        }

        /// <summary>
        /// إضافة قسم رسالة السائقين
        /// </summary>
        private static void AddDriverMessageSection(Section section)
        {
            var messageTable = section.AddTable();
            messageTable.Borders.Width = 1;
            messageTable.Borders.Color = Colors.Black;
            messageTable.AddColumn("16cm");

            var messageRow = messageTable.AddRow();
            messageRow.Cells[0].Shading.Color = Colors.LightBlue;
            var messageTitle = messageRow.Cells[0].AddParagraph("خط السير ووصف الرسالة المرسلة للسائقين");
            messageTitle.Format.Font.Bold = true;
            messageTitle.Format.Alignment = ParagraphAlignment.Center;
            messageTitle.Format.Font.Size = 12;

            var contentRow = messageTable.AddRow();
            contentRow.Height = "2cm";
            var contentPara = contentRow.Cells[0].AddParagraph("لم يتم تحديد السائق المناسب بعد");
            contentPara.Format.Alignment = ParagraphAlignment.Right;
            contentPara.Format.Font.Size = 11;

            section.AddParagraph().Format.SpaceAfter = 15;
        }

        /// <summary>
        /// إضافة قسم تواريخ الزيارة
        /// </summary>
        private static void AddVisitDatesSection(Section section, ReportViewModel viewModel)
        {
            var datesTable = section.AddTable();
            datesTable.Borders.Width = 1;
            datesTable.Borders.Color = Colors.Black;

            // أعمدة التواريخ
            datesTable.AddColumn("4cm");
            datesTable.AddColumn("4cm");
            datesTable.AddColumn("4cm");
            datesTable.AddColumn("4cm");

            var datesRow = datesTable.AddRow();
            datesRow.Height = "1cm";

            // عدد الأيام
            var daysCell = datesRow.Cells[0];
            daysCell.AddParagraph("عدد الأيام: 4 يوم").Format.Alignment = ParagraphAlignment.Center;

            // تاريخ العودة
            var returnCell = datesRow.Cells[1];
            returnCell.AddParagraph($"تاريخ العودة: {DateTime.Now.AddDays(4):dd/MM/yyyy}").Format.Alignment = ParagraphAlignment.Center;

            // تاريخ النزول
            var departureCell = datesRow.Cells[2];
            departureCell.AddParagraph($"تاريخ النزول: {DateTime.Now:dd/MM/yyyy}").Format.Alignment = ParagraphAlignment.Center;

            // أيقونة التقويم (نص بديل)
            var iconCell = datesRow.Cells[3];
            iconCell.AddParagraph("📅").Format.Alignment = ParagraphAlignment.Center;
        }

        /// <summary>
        /// إنشاء مستند التقرير
        /// </summary>
        private static Document CreateReportDocument(ReportViewModel viewModel)
        {
            // إنشاء المستند
            var document = new Document();
            document.Info.Title = "تقرير الزيارة الميدانية";
            document.Info.Author = "صندوق التنمية الاجتماعية";
            document.Info.Subject = $"زيارة رقم {viewModel.ReportData.VisitNumber}";

            // تعريف الأنماط
            DefineStyles(document);

            // إضافة القسم الرئيسي
            var section = document.AddSection();
            SetupPageLayout(section);

            // إضافة محتوى التقرير
            AddReportHeader(section);
            AddReportTitle(section, viewModel);
            AddVisitInformation(section, viewModel);
            AddProjectsTable(section, viewModel);
            AddOffersTable(section, viewModel);
            AddReportFooter(section);

            return document;
        }

        /// <summary>
        /// تعريف أنماط المستند
        /// </summary>
        private static void DefineStyles(Document document)
        {
            // النمط العادي
            var style = document.Styles["Normal"];
            style.Font.Name = "Times New Roman"; // خط آمن ومدعوم
            style.Font.Size = 12;

            // نمط العنوان الرئيسي
            style = document.Styles.AddStyle("Title", "Normal");
            style.Font.Size = 18;
            style.Font.Bold = true;
            style.ParagraphFormat.Alignment = ParagraphAlignment.Center;
            style.ParagraphFormat.SpaceAfter = 20;

            // نمط العنوان الفرعي
            style = document.Styles.AddStyle("Subtitle", "Normal");
            style.Font.Size = 14;
            style.Font.Bold = true;
            style.ParagraphFormat.SpaceAfter = 10;
            style.ParagraphFormat.SpaceBefore = 15;

            // نمط رأس الجدول
            style = document.Styles.AddStyle("TableHeader", "Normal");
            style.Font.Bold = true;
            style.ParagraphFormat.Alignment = ParagraphAlignment.Center;

            // نمط خلية الجدول
            style = document.Styles.AddStyle("TableCell", "Normal");
            style.ParagraphFormat.Alignment = ParagraphAlignment.Center;
        }

        /// <summary>
        /// إعداد تخطيط الصفحة
        /// </summary>
        private static void SetupPageLayout(Section section)
        {
            // إعداد الصفحة
            section.PageSetup.PageFormat = PageFormat.A4;
            section.PageSetup.Orientation = Orientation.Portrait;
            section.PageSetup.TopMargin = "2cm";
            section.PageSetup.BottomMargin = "2cm";
            section.PageSetup.LeftMargin = "2cm";
            section.PageSetup.RightMargin = "2cm";

            // ترقيم الصفحات
            var paragraph = section.Footers.Primary.AddParagraph();
            paragraph.AddText("صفحة ");
            paragraph.AddPageField();
            paragraph.AddText(" من ");
            paragraph.AddNumPagesField();
            paragraph.Format.Alignment = ParagraphAlignment.Center;
        }

        /// <summary>
        /// إضافة رأس التقرير
        /// </summary>
        private static void AddReportHeader(Section section)
        {
            var table = section.AddTable();
            table.Style = "Table";
            table.Borders.Width = 0;
            table.Rows.LeftIndent = 0;

            // تعريف الأعمدة
            var column = table.AddColumn("5cm");
            column.Format.Alignment = ParagraphAlignment.Left;
            column = table.AddColumn("8cm");
            column.Format.Alignment = ParagraphAlignment.Center;
            column = table.AddColumn("5cm");
            column.Format.Alignment = ParagraphAlignment.Right;

            // إضافة الصف
            var row = table.AddRow();
            row.Cells[0].AddParagraph("🏢 SFD");
            row.Cells[1].AddParagraph("صندوق التنمية الاجتماعية").Style = "TableHeader";
            row.Cells[2].AddParagraph($"التاريخ: {DateTime.Now:yyyy/MM/dd}");

            // خط فاصل
            var separator = section.AddParagraph();
            separator.Format.Borders.Bottom.Width = 1;
            separator.Format.SpaceAfter = 15;
        }

        /// <summary>
        /// إضافة عنوان التقرير
        /// </summary>
        private static void AddReportTitle(Section section, ReportViewModel viewModel)
        {
            var title = section.AddParagraph("تقرير الزيارة الميدانية");
            title.Style = "Title";
        }

        /// <summary>
        /// إضافة معلومات الزيارة
        /// </summary>
        private static void AddVisitInformation(Section section, ReportViewModel viewModel)
        {
            var subtitle = section.AddParagraph("معلومات الزيارة");
            subtitle.Style = "Subtitle";

            var table = section.AddTable();
            table.Style = "Table";
            table.Borders.Width = 0.75;
            table.Rows.LeftIndent = 0;

            // تعريف الأعمدة
            var column = table.AddColumn("4cm");
            column.Format.Alignment = ParagraphAlignment.Right;
            column = table.AddColumn("10cm");
            column.Format.Alignment = ParagraphAlignment.Right;

            // إضافة البيانات
            AddInfoRow(table, "رقم الزيارة:", viewModel.ReportData.VisitNumber?.ToString() ?? "غير محدد");
            AddInfoRow(table, "التاريخ:", DateTime.Now.ToString("yyyy/MM/dd"));
            AddInfoRow(table, "الوقت:", DateTime.Now.ToString("HH:mm"));
            AddInfoRow(table, "القطاع:", "قطاع التنمية");
            AddInfoRow(table, "مسؤول الزيارة:", "مسؤول الزيارة الميدانية");

            table.SetEdge(0, 0, 2, table.Rows.Count, Edge.Box, BorderStyle.Single, 0.75, Colors.Black);
        }

        /// <summary>
        /// إضافة صف معلومات
        /// </summary>
        private static void AddInfoRow(Table table, string label, string value)
        {
            var row = table.AddRow();
            row.Cells[0].AddParagraph(label).Style = "TableHeader";
            row.Cells[0].Shading.Color = Colors.LightGray;
            row.Cells[1].AddParagraph(value).Style = "TableCell";
        }

        /// <summary>
        /// إضافة جدول المشاريع
        /// </summary>
        private static void AddProjectsTable(Section section, ReportViewModel viewModel)
        {
            if (viewModel.ReportData.Projects?.Count == 0)
                return;

            var subtitle = section.AddParagraph("المشاريع");
            subtitle.Style = "Subtitle";

            var table = section.AddTable();
            table.Style = "Table";
            table.Borders.Width = 0.75;
            table.Rows.LeftIndent = 0;
            table.KeepTogether = true; // منع تقسيم الجدول

            // تعريف الأعمدة
            table.AddColumn("2cm").Format.Alignment = ParagraphAlignment.Center;
            table.AddColumn("4cm").Format.Alignment = ParagraphAlignment.Center;
            table.AddColumn("8cm").Format.Alignment = ParagraphAlignment.Center;

            // رأس الجدول
            var headerRow = table.AddRow();
            headerRow.HeadingFormat = true;
            headerRow.Format.Font.Bold = true;
            headerRow.Shading.Color = Colors.LightGray;
            headerRow.Cells[0].AddParagraph("رقم").Style = "TableHeader";
            headerRow.Cells[1].AddParagraph("رقم المشروع").Style = "TableHeader";
            headerRow.Cells[2].AddParagraph("اسم المشروع").Style = "TableHeader";

            // بيانات الجدول
            int projectIndex = 1;
            foreach (var project in viewModel.ReportData.Projects)
            {
                var row = table.AddRow();
                row.KeepWith = 1; // منع تقسيم الصف
                row.Cells[0].AddParagraph(projectIndex.ToString()).Style = "TableCell";
                row.Cells[1].AddParagraph(project.ProjectNumber ?? "").Style = "TableCell";
                row.Cells[2].AddParagraph(project.ProjectName ?? "").Style = "TableCell";
                projectIndex++;
            }

            table.SetEdge(0, 0, 3, table.Rows.Count, Edge.Box, BorderStyle.Single, 0.75, Colors.Black);
        }

        /// <summary>
        /// إضافة جدول عروض الأسعار
        /// </summary>
        private static void AddOffersTable(Section section, ReportViewModel viewModel)
        {
            if (viewModel.ReportData.PriceOffers?.Count == 0)
                return;

            var subtitle = section.AddParagraph("عروض الأسعار");
            subtitle.Style = "Subtitle";

            var table = section.AddTable();
            table.Style = "Table";
            table.Borders.Width = 0.75;
            table.Rows.LeftIndent = 0;
            table.KeepTogether = true;

            // تعريف الأعمدة
            table.AddColumn("2cm").Format.Alignment = ParagraphAlignment.Center;
            table.AddColumn("4cm").Format.Alignment = ParagraphAlignment.Center;
            table.AddColumn("4cm").Format.Alignment = ParagraphAlignment.Center;
            table.AddColumn("4cm").Format.Alignment = ParagraphAlignment.Center;

            // رأس الجدول
            var headerRow = table.AddRow();
            headerRow.HeadingFormat = true;
            headerRow.Format.Font.Bold = true;
            headerRow.Shading.Color = Colors.LightGray;
            headerRow.Cells[0].AddParagraph("رقم").Style = "TableHeader";
            headerRow.Cells[1].AddParagraph("اسم السائق").Style = "TableHeader";
            headerRow.Cells[2].AddParagraph("رقم الهاتف").Style = "TableHeader";
            headerRow.Cells[3].AddParagraph("السعر").Style = "TableHeader";

            // بيانات الجدول
            int offerIndex = 1;
            foreach (var offer in viewModel.ReportData.PriceOffers)
            {
                var row = table.AddRow();
                row.KeepWith = 1;
                row.Cells[0].AddParagraph(offerIndex.ToString()).Style = "TableCell";
                row.Cells[1].AddParagraph(offer.DriverName ?? "").Style = "TableCell";
                row.Cells[2].AddParagraph(offer.PhoneNumber ?? "").Style = "TableCell";
                row.Cells[3].AddParagraph(offer.OfferedPrice.ToString("N0")).Style = "TableCell";
                offerIndex++;
            }

            table.SetEdge(0, 0, 4, table.Rows.Count, Edge.Box, BorderStyle.Single, 0.75, Colors.Black);
        }

        /// <summary>
        /// إضافة تذييل التقرير
        /// </summary>
        private static void AddReportFooter(Section section)
        {
            // مساحة فارغة
            section.AddParagraph().Format.SpaceAfter = 30;

            // جدول التوقيعات
            var table = section.AddTable();
            table.Style = "Table";
            table.Borders.Width = 0;
            table.Rows.LeftIndent = 0;

            table.AddColumn("9cm").Format.Alignment = ParagraphAlignment.Center;
            table.AddColumn("9cm").Format.Alignment = ParagraphAlignment.Center;

            var row = table.AddRow();
            row.Cells[0].AddParagraph("توقيع المسؤول\n\n_________________");
            row.Cells[1].AddParagraph("توقيع المراجع\n\n_________________");
        }

        /// <summary>
        /// حفظ كـ PDF
        /// </summary>
        private static void SaveAsPdf(Document document)
        {
            try
            {
                var saveDialog = new Microsoft.Win32.SaveFileDialog
                {
                    Filter = "PDF Files (*.pdf)|*.pdf",
                    DefaultExt = "pdf",
                    FileName = $"تقرير_زيارة_ميدانية_{DateTime.Now:yyyyMMdd_HHmm}.pdf"
                };

                if (saveDialog.ShowDialog() == true)
                {
                    var renderer = new PdfDocumentRenderer();
                    renderer.Document = document;
                    renderer.RenderDocument();
                    renderer.PdfDocument.Save(saveDialog.FileName);

                    MessageBox.Show($"تم حفظ التقرير بنجاح:\n{saveDialog.FileName}", "نجح الحفظ", MessageBoxButton.OK, MessageBoxImage.Information);

                    // فتح الملف
                    Process.Start(new ProcessStartInfo(saveDialog.FileName) { UseShellExecute = true });
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ PDF:\n{ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// طباعة مباشرة
        /// </summary>
        private static void PrintDirectly(Document document)
        {
            try
            {
                var renderer = new PdfDocumentRenderer();
                renderer.Document = document;
                renderer.RenderDocument();

                // حفظ مؤقت للطباعة
                var tempFile = Path.GetTempFileName().Replace(".tmp", ".pdf");
                renderer.PdfDocument.Save(tempFile);

                // طباعة الملف
                Process.Start(new ProcessStartInfo(tempFile) { UseShellExecute = true, Verb = "print" });

                MessageBox.Show("تم إرسال التقرير للطباعة", "طباعة", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في الطباعة:\n{ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}
