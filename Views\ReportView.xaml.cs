using System;
using System.Windows;
using System.Windows.Controls;
using DriverManagementSystem.ViewModels;
using DriverManagementSystem.Services;
using MigraDoc.DocumentObjectModel;
using System.Printing;

namespace DriverManagementSystem.Views
{
    public partial class ReportView : UserControl
    {
        public ReportView()
        {
            InitializeComponent();
            // إعداد DataContext افتراضي
            if (DataContext == null)
            {
                DataContext = new ReportViewModel();
            }
        }

        /// <summary>
        /// ctor للطباعة مع DataContext محدد
        /// </summary>
        public ReportView(object dataContext)
        {
            InitializeComponent();
            DataContext = dataContext;
            System.Diagnostics.Debug.WriteLine($"🔧 تم إنشاء ReportView مع DataContext محدد: {dataContext?.GetType().Name}");
        }

        /// <summary>
        /// طباعة مُقسمة إلى صفحات A4
        /// </summary>
        private void PrintReportButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // استخدام خدمة الطباعة المُقسمة مع ضبط A4
                ReportViewPrintService.PrintPaginatedGeneral(this, PageMediaSizeName.ISOA4);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في طباعة التقرير: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// طباعة احترافية إلى PDF بحجم A4
        /// </summary>
        private void ProfessionalPrintButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (DataContext is ReportViewModel vm)
                {
                    ProfessionalReportService.PrintFieldVisitReport(vm, PageFormat.A4);
                }
                else
                {
                    MessageBox.Show("لا توجد بيانات للطباعة", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في الطباعة الاحترافية: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// معاينة الطباعة
        /// </summary>
        private void PrintPreviewButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                ReportViewPrintService.ShowPrintPreview(this, PageMediaSizeName.ISOA4);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في معاينة الطباعة: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// فتح محرر العقد
        /// </summary>
        private void EditContractButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var dlg = new ContractEditorWindow();
                dlg.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نافذة تحرير العقد: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}
